package so.appio.app.network

import android.util.Log
import kotlinx.serialization.json.Json
import kotlinx.coroutines.flow.first
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.DeviceDataStore
import so.appio.app.data.entity.device.*
import so.appio.app.data.entity.service.*
import so.appio.app.data.entity.notification.*
import so.appio.app.data.entity.widget.*
import so.appio.app.data.entity.featureflags.*
import so.appio.app.data.entity.fingerprint.*
import so.appio.app.data.entity.AnyDecodable
import so.appio.app.utils.DeviceInfoManager.DeviceInfo

/**
 * Singleton service layer handling API operations with simple debounce logic.
 */
object APIService {
    private const val TAG = "LOG:APIService"
    private val debounce = mutableMapOf<String, Long>()
    private const val MINIMUM_INTERVAL_MS = 2000L

    // JSON configuration that includes default values in serialization
    private val json = Json {
        encodeDefaults = true
    }

    @Synchronized
    private fun checkDebounce(function: String, vararg params: String) {
        val key = (listOf(function) + params).joinToString("-")
        val now = System.currentTimeMillis()
        val lastCall = debounce[key]
        if (lastCall != null && now - lastCall < MINIMUM_INTERVAL_MS) {
            Log.w(TAG, "Debounce: $key")
            throw APIError.Debounce
        }
        debounce[key] = now
    }

    suspend fun fingerprintMatch(request: FingerprintRequest): FingerprintResponse? {
        checkDebounce("fingerprintMatch")
        val data = json.encodeToString(request)
        return APIClient.performRequest(
            endpoint = "/fingerprints/match",
            method = "POST",
            body = data,
        )
    }

    suspend fun registerDevice(serviceId: String, customerUserId: String, deviceInfo: DeviceInfo): DeviceResponse {
        checkDebounce("registerDevice")
        val request = NewDeviceRequest(
            customerUserId = customerUserId,
            name = deviceInfo.name,
            platform = "android",
            osVersion = deviceInfo.osVersion,
            deviceIdentifier = deviceInfo.deviceIdentifier,
            model = deviceInfo.manufacturerModel,
            notificationsEnabled = deviceInfo.notificationsEnabled,
        )
        val data = json.encodeToString(request)
        val response: DeviceResponse? = APIClient.performRequest(
            endpoint = "/devices",
            method = "POST",
            headers = mapOf("X-Service-Id" to serviceId),
            body = data,
            expectedStatusCode = 201,
        )
        return response ?: throw APIError.BadServerResponse
    }

    // TODO: call when needed
    suspend fun updateDevice(deviceId: String, serviceId: String, deviceDataStore: DeviceDataStore): Boolean {
        val request = UpdateDeviceRequest(
            data = UpdateDeviceData(
                notificationsEnabled = deviceDataStore.notificationEnabled.first(),
                deviceToken = deviceDataStore.deviceToken.first() ?: "",
            )
        )
        val data = json.encodeToString(request)
        APIClient.performRequest<AnyDecodable>(
            endpoint = "/devices/$deviceId",
            method = "PATCH",
            headers = mapOf("X-Service-Id" to serviceId),
            body = data,
        )
        return true
    }

    suspend fun linkService(deviceId: String, serviceId: String, customerUserId: String): Boolean {
        checkDebounce("linkService", deviceId, serviceId, customerUserId)
        val request = LinkServiceRequest(customerUserId)
        val data = json.encodeToString(request)
        APIClient.performRequest<AnyDecodable>(
            endpoint = "/devices/${'$'}deviceId/services",
            method = "POST",
            headers = mapOf("X-Service-Id" to serviceId),
            body = data,
        )
        return true
    }

    suspend fun detachService(serviceId: String, deviceId: String): Boolean {
        checkDebounce("detachService", deviceId, serviceId)
        APIClient.performRequest<AnyDecodable>(
            endpoint = "/devices/${'$'}deviceId",
            method = "DELETE",
            headers = mapOf("X-Service-Id" to serviceId),
        )
        return true
    }

    suspend fun fetchWidget(serviceId: String, widgetId: String): WidgetResponse {
        checkDebounce("fetchWidget", serviceId, widgetId)
        val response: WidgetResponse? = APIClient.performRequest(
            endpoint = "/widgets/${'$'}widgetId",
            method = "GET",
            headers = mapOf("X-Service-Id" to serviceId),
        )
        return response ?: throw APIError.BadServerResponse
    }

    suspend fun fetchServiceWithWidgets(serviceId: String): ServiceResponse {
        checkDebounce("fetchServiceWithWidgets", serviceId)
        val response: ServiceResponse? = APIClient.performRequest(
            endpoint = "/services/${'$'}serviceId",
            method = "GET",
            headers = mapOf("X-Service-Id" to serviceId),
        )
        return response ?: throw APIError.BadServerResponse
    }

    suspend fun fetchAllServicesWithWidgets(deviceId: String): List<ServiceResponse> {
        checkDebounce("fetchAllServices", deviceId)
        val list: List<ServiceResponse>? = APIClient.performRequest(
            endpoint = "/services",
            method = "GET",
            headers = mapOf("X-Device-Id" to deviceId),
        )
        return list ?: emptyList()
    }

    suspend fun fetchAllNotifications(serviceId: String, deviceId: String, cursor: String): List<NotificationResponse> {
        checkDebounce("fetchAllNotifications", serviceId, deviceId)
        val list: List<NotificationResponse>? = APIClient.performRequest(
            endpoint = "/notifications?cursor=${'$'}cursor",
            method = "GET",
            headers = mapOf(
                "X-Service-Id" to serviceId,
                "X-Device-Id" to deviceId,
            ),
        )
        return list ?: emptyList()
    }

    suspend fun notificationDelivered(notification: Notification, deviceId: String): Boolean {
        checkDebounce("notificationDelivered", notification.id, deviceId)
        APIClient.performRequest<AnyDecodable>(
            endpoint = "/notifications/${'$'}{notification.id}",
            method = "PATCH",
            headers = mapOf(
                "X-Service-Id" to notification.serviceId,
                "X-Device-Id" to deviceId,
            ),
        )
        return true
    }

    suspend fun fetchFeatureFlags(): FeatureFlagsEntity? {
        checkDebounce("fetchFeatureFlags")
        val response: FeatureFlagsEntity? = APIClient.performRequest(
            endpoint = "/ff",
            method = "GET",
        )
        return response
    }
}
